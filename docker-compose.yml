version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: loom-postgres
    environment:
      POSTGRES_DB: loom
      POSTGRES_USER: loom_user
      POSTGRES_PASSWORD: loom_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U loom_user -d loom"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: loom-backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
    environment:
      - DATABASE_URL=**************************************************/loom
      - PYTHONPATH=/app
    env_file:
      - ./backend/.env
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: loom-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  postgres_data:
