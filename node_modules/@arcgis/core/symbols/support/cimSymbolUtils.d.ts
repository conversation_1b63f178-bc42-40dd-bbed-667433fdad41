import "../../interfaces";

export const applyCIMSymbolColor: __esri.cimSymbolUtils["applyCIMSymbolColor"];
export const applyCIMSymbolRotation: __esri.cimSymbolUtils["applyCIMSymbolRotation"];
export const getCIMSymbolColor: __esri.cimSymbolUtils["getCIMSymbolColor"];
export const getCIMSymbolRotation: __esri.cimSymbolUtils["getCIMSymbolRotation"];
export const getCIMSymbolSize: __esri.cimSymbolUtils["getCIMSymbolSize"];
export const scaleCIMSymbolTo: __esri.cimSymbolUtils["scaleCIMSymbolTo"];
