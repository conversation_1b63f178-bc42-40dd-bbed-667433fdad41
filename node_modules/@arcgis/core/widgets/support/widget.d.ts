import "../../interfaces";

export const accessibleHandler: typeof __esri.widget.accessibleHandler;
export const cssTransition: typeof __esri.widget.cssTransition;
export const isActivationKey: typeof __esri.widget.isActivationKey;
export const isRTL: typeof __esri.widget.isRTL;
export const messageBundle: typeof __esri.widget.messageBundle;
export const storeNode: typeof __esri.widget.storeNode;
export const vmEvent: typeof __esri.widget.vmEvent;
export const tsx: typeof __esri.widget.tsx;
export namespace tsx {
namespace JSX {
interface IntrinsicElements {
  [elementName: string]: any;
}

interface Element {}
}
}
