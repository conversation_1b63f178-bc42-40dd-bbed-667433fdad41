.PHONY: dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend lint-frontend-fix build build-backend build-frontend

dev-start:
	docker-compose up --build -d

dev-stop:
	docker-compose down

test: test-backend test-frontend

test-backend:
	docker-compose exec backend python -m pytest tests/ -v --cov=server --cov-report=html --cov-report=term

test-frontend:
	docker-compose run --rm frontend npm test -- --run

lint: lint-backend lint-frontend

lint-backend:
	docker-compose exec backend python -m black server/ tests/ scripts/
	docker-compose exec backend python -m flake8 server/ tests/ scripts/
	docker-compose exec backend python -m mypy server/

lint-frontend:
	docker build --target build -t loom-frontend-lint frontend/
	docker run --rm loom-frontend-lint npm run lint

lint-frontend-fix:
	docker build --target build -t loom-frontend-lint frontend/
	docker run --rm loom-frontend-lint npm run lint:fix

build:
	docker-compose build

build-backend:
	docker-compose build backend

build-frontend:
	docker-compose build frontend
