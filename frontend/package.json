{"name": "loom-frontend", "version": "0.1.0", "private": true, "dependencies": {"@deck.gl/core": "^9.1.12", "@deck.gl/layers": "^9.1.12", "@deck.gl/react": "^9.1.12", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "deck.gl": "^9.1.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^2.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "msw": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "react-scripts test --coverage --watchAll=false --ci", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}