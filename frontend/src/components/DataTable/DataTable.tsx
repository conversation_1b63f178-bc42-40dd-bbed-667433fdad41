import React, { useState } from 'react';
import './DataTable.css';
import { EmbeddingPoint3D } from '../../services/api';

interface DataTableProps {
  data: EmbeddingPoint3D[];
  loading: boolean;
  selectedRows?: string[];
  onRowSelect?: (pointId: string) => void;
}

const DataTable: React.FC<DataTableProps> = ({
  data,
  loading,
  selectedRows: externalSelectedRows = [],
  onRowSelect
}) => {
  const [internalSelectedRows, setInternalSelectedRows] = useState<Set<string>>(new Set());
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Use external selection if provided, otherwise use internal state
  const selectedRows = externalSelectedRows.length > 0
    ? new Set(externalSelectedRows)
    : internalSelectedRows;

  const handleRowSelect = (id: string) => {
    if (onRowSelect) {
      // Use external handler
      onRowSelect(id);
    } else {
      // Use internal state
      const newSelected = new Set(internalSelectedRows);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      setInternalSelectedRows(newSelected);
    }
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (column: string) => {
    if (sortColumn !== column) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="data-table">
      <div className="table-header">
        <div className="table-info">
          <span>
            {loading ? 'Loading...' : `Showing ${data.length} points`}
          </span>
          {selectedRows.size > 0 && (
            <span className="selection-info">
              ({selectedRows.size} selected)
            </span>
          )}
        </div>
        <div className="table-actions">
          <button className="action-button">Export Selected</button>
          <button className="action-button">Clear Selection</button>
        </div>
      </div>

      <div className="table-container">
        <table className="points-table">
          <thead>
            <tr>
              <th className="checkbox-column">
                <input type="checkbox" />
              </th>
              <th onClick={() => handleSort('id')} className="sortable">
                ID {getSortIcon('id')}
              </th>
              <th onClick={() => handleSort('x')} className="sortable">
                X {getSortIcon('x')}
              </th>
              <th onClick={() => handleSort('y')} className="sortable">
                Y {getSortIcon('y')}
              </th>
              <th onClick={() => handleSort('z')} className="sortable">
                Z {getSortIcon('z')}
              </th>
              <th onClick={() => handleSort('point_radius')} className="sortable">
                Radius {getSortIcon('point_radius')}
              </th>
              <th onClick={() => handleSort('image_robot_id')} className="sortable">
                Robot ID {getSortIcon('image_robot_id')}
              </th>
              <th onClick={() => handleSort('method')} className="sortable">
                Method {getSortIcon('method')}
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((point) => (
              <tr
                key={point.id}
                className={selectedRows.has(point.image_id.toString()) ? 'selected' : ''}
              >
                <td className="checkbox-column">
                  <input
                    type="checkbox"
                    checked={selectedRows.has(point.image_id.toString())}
                    onChange={() => handleRowSelect(point.image_id.toString())}
                  />
                </td>
                <td>{point.id}</td>
                <td>{point.x.toFixed(3)}</td>
                <td>{point.y.toFixed(3)}</td>
                <td>{point.z?.toFixed(3) || 'N/A'}</td>
                <td>{point.point_radius.toFixed(1)}</td>
                <td>{point.image_robot_id}</td>
                <td>{point.method}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DataTable;
