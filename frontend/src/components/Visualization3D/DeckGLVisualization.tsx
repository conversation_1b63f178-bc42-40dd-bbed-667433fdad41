import React, { useMemo } from 'react';
import DeckG<PERSON> from '@deck.gl/react';
import { <PERSON><PERSON><PERSON><PERSON>lotLayer } from '@deck.gl/layers';
import { OrthographicView, COORDINATE_SYSTEM } from '@deck.gl/core';
import { EmbeddingPoint3D } from '../../services/api';

interface DeckGLVisualizationProps {
  data: EmbeddingPoint3D[];
  selectedPoints?: string[];
  onPointClick?: (point: EmbeddingPoint3D) => void;
  onPointHover?: (point: EmbeddingPoint3D | null) => void;
  width?: number;
  height?: number;
}

const DeckGLVisualization: React.FC<DeckGLVisualizationProps> = ({
  data,
  selectedPoints = [],
  onPointClick,
  onPointHover,
  width = 800,
  height = 600,
}) => {
  // Calculate bounds for proper initial view
  const bounds = useMemo(() => {
    if (data.length === 0) {
      return { minX: -1, maxX: 1, minY: -1, maxY: 1, minZ: -1, maxZ: 1 };
    }

    const xs = data.map(d => d.x);
    const ys = data.map(d => d.y);
    const zs = data.map(d => d.z || 0).filter(z => z !== undefined);

    return {
      minX: Math.min(...xs),
      maxX: Math.max(...xs),
      minY: Math.min(...ys),
      maxY: Math.max(...ys),
      minZ: zs.length > 0 ? Math.min(...zs) : -1,
      maxZ: zs.length > 0 ? Math.max(...zs) : 1,
    };
  }, [data]);

  // Calculate center and zoom for initial view
  const initialViewState = useMemo(() => {
    const centerX = (bounds.minX + bounds.maxX) / 2;
    const centerY = (bounds.minY + bounds.maxY) / 2;
    const centerZ = (bounds.minZ + bounds.maxZ) / 2;

    const rangeX = bounds.maxX - bounds.minX;
    const rangeY = bounds.maxY - bounds.minY;
    const rangeZ = bounds.maxZ - bounds.minZ;
    const maxRange = Math.max(rangeX, rangeY, rangeZ);

    // Calculate zoom to fit all points
    const zoom = maxRange > 0 ? Math.log2(Math.min(width, height) / (maxRange * 1.2)) : 0;

    return {
      target: [centerX, centerY, centerZ] as [number, number, number],
      zoom: Math.max(zoom, -10), // Prevent extreme zoom out
      minZoom: -15,
      maxZoom: 15,
    };
  }, [bounds, width, height]);

  // Create the scatterplot layer
  const layers = useMemo(() => [
    new ScatterplotLayer({
      id: 'scatterplot-layer',
      data,
      coordinateSystem: COORDINATE_SYSTEM.CARTESIAN,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getRadius: 50, // Base radius in coordinate units
      radiusScale: Math.max((bounds.maxX - bounds.minX) / 1000, 0.001), // Scale based on data range
      radiusMinPixels: 2,
      radiusMaxPixels: 8,
      getColor: (d: EmbeddingPoint3D) => {
        // Color based on selection state
        if (selectedPoints.includes(d.image_id.toString())) {
          return [255, 165, 0, 255]; // Orange for selected
        }

        // Color based on robot_id or use a default gradient
        if (d.image_robot_id) {
          const hash = d.image_robot_id.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
          }, 0);
          return [
            Math.abs(hash) % 255,
            Math.abs(hash >> 8) % 255,
            Math.abs(hash >> 16) % 255,
            200
          ];
        }

        // Default blue color
        return [100, 150, 255, 200];
      },
      pickable: true,
      onClick: (info) => {
        if (info.object && onPointClick) {
          onPointClick(info.object);
        }
      },
      onHover: (info) => {
        if (onPointHover) {
          onPointHover(info.object || null);
        }
      },
      updateTriggers: {
        getColor: [selectedPoints],
      },
    }),
  ], [data, selectedPoints, bounds, onPointClick, onPointHover]);

  return (
    <div style={{ width, height, position: 'relative' }}>
      <DeckGL
        width={width}
        height={height}
        initialViewState={initialViewState}
        controller={true}
        layers={layers}
        views={new OrthographicView()}
        getCursor={({ isDragging, isHovering }) => {
          if (isDragging) return 'grabbing';
          if (isHovering) return 'pointer';
          return 'grab';
        }}
      />
      
      {/* Loading overlay when no data */}
      {data.length === 0 && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            fontSize: '16px',
            color: '#666',
          }}
        >
          No data to display
        </div>
      )}
      
      {/* Point count indicator */}
      {data.length > 0 && (
        <div
          style={{
            position: 'absolute',
            top: 10,
            left: 10,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'monospace',
          }}
        >
          {data.length.toLocaleString()} points
        </div>
      )}
    </div>
  );
};

export default DeckGLVisualization;
