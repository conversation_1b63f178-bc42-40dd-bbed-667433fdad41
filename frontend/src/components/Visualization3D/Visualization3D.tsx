import React, { useState, useCallback, useRef, useEffect } from 'react';
import './Visualization3D.css';
import { VisualizationResponse } from '../../services/api';
import DeckGLVisualization from './DeckGLVisualization';

interface Visualization3DProps {
  data: VisualizationResponse | null;
  loading: boolean;
  error: string | null;
  selectedPoints?: string[];
  onPointSelect?: (pointId: string) => void;
}

const Visualization3D: React.FC<Visualization3DProps> = ({
  data,
  loading,
  error,
  selectedPoints = [],
  onPointSelect
}) => {
  const [hoveredPoint, setHoveredPoint] = useState<any>(null);
  const [containerDimensions, setContainerDimensions] = useState({ width: 800, height: 600 });
  const containerRef = useRef<HTMLDivElement>(null);

  const handlePointClick = useCallback((point: any) => {
    if (onPointSelect) {
      onPointSelect(point.image_id);
    }
  }, [onPointSelect]);

  const handlePointHover = useCallback((point: any) => {
    setHoveredPoint(point);
  }, []);

  // Measure container dimensions and handle resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setContainerDimensions({ width, height });
      }
    };

    // Initial measurement
    updateDimensions();

    // Set up ResizeObserver for dynamic resizing
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Also listen to window resize as fallback
    window.addEventListener('resize', updateDimensions);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);
  if (error) {
    return (
      <div className="visualization-3d">
        <div className="visualization-placeholder">
          <div className="placeholder-content">
            <h2>Error Loading Data</h2>
            <p>{error}</p>
            <button className="control-button" onClick={() => window.location.reload()}>
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="visualization-3d">
      {data && data.points && data.points.length > 0 ? (
        <div className="visualization-content">
          <div className="visualization-header">
            <h3>3D Visualization - {data.method}</h3>
            <span className="point-count">Showing {data.points.length} points</span>
          </div>

          <div className="visualization-main" ref={containerRef}>
            <DeckGLVisualization
              data={data.points}
              selectedPoints={selectedPoints}
              onPointClick={handlePointClick}
              onPointHover={handlePointHover}
              width={containerDimensions.width}
              height={containerDimensions.height}
            />
          </div>

          {/* Hover tooltip */}
          {hoveredPoint && (
            <div className="hover-tooltip">
              <strong>Image ID:</strong> {hoveredPoint.image_id}<br />
              <strong>Robot:</strong> {hoveredPoint.image_robot_id}<br />
              <strong>Position:</strong> ({hoveredPoint.x.toFixed(3)}, {hoveredPoint.y.toFixed(3)}, {(hoveredPoint.z || 0).toFixed(3)})
            </div>
          )}
        </div>
      ) : (
        <div className="visualization-placeholder">
          <div className="placeholder-content">
            <h2>3D Visualization</h2>
            <p>
              {data ? `${data.method} visualization` : '3D scatter plot will be rendered here'}
            </p>
            <div className="placeholder-stats">
              <div className="stat">
                <span className="stat-label">Points:</span>
                <span className="stat-value">{data?.total_points || 0}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Method:</span>
                <span className="stat-value">{data?.method || 'None'}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Model:</span>
                <span className="stat-value">{data?.model_id || 'None'}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading overlay */}
      <div className="loading-overlay" style={{ display: loading ? 'flex' : 'none' }}>
        <div className="loading-spinner"></div>
        <p>Loading visualization...</p>
      </div>
    </div>
  );
};

export default Visualization3D;
