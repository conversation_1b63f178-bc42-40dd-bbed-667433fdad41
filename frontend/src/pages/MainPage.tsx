import React, { useState, useRef, useCallback } from 'react';
import './MainPage.css';
import FilterPanel from '../components/FilterPanel/FilterPanel';
import Visualization3D from '../components/Visualization3D/Visualization3D';
import DataTable from '../components/DataTable/DataTable';
import { useVisualizationData } from '../hooks/useVisualizationData';
import { useFilterOptions } from '../hooks/useFilterOptions';
import { VisualizationFilters } from '../services/api';

const MainPage: React.FC = () => {
  const [isFilterPanelCollapsed, setIsFilterPanelCollapsed] = useState(false);
  const [isDataTableCollapsed, setIsDataTableCollapsed] = useState(false);
  const [filterPanelHeight, setFilterPanelHeight] = useState(200); // Default height in pixels
  const [dataTableHeight, setDataTableHeight] = useState(300); // Default height in pixels
  const [currentFilters, setCurrentFilters] = useState<VisualizationFilters>({});
  const [selectedPoints, setSelectedPoints] = useState<string[]>([]);

  const isDraggingFilter = useRef(false);
  const isDraggingTable = useRef(false);
  const startY = useRef(0);
  const startHeight = useRef(0);

  // Fetch visualization data and filter options
  const { data: visualizationData, loading: dataLoading, error: dataError, refetch } = useVisualizationData(currentFilters);
  const { options: filterOptions, loading: optionsLoading } = useFilterOptions();

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: VisualizationFilters) => {
    setCurrentFilters(newFilters);
    refetch(newFilters);
  }, [refetch]);

  // Handle point selection
  const handlePointSelect = useCallback((pointId: string) => {
    setSelectedPoints(prev => {
      if (prev.includes(pointId)) {
        return prev.filter(id => id !== pointId);
      } else {
        return [...prev, pointId];
      }
    });
  }, []);

  const handleFilterResize = useCallback((e: MouseEvent) => {
    if (!isDraggingFilter.current) return;
    const deltaY = e.clientY - startY.current;
    const newHeight = Math.max(50, Math.min(400, startHeight.current + deltaY));
    setFilterPanelHeight(newHeight);
  }, []);

  const handleFilterResizeEnd = useCallback(() => {
    isDraggingFilter.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.removeEventListener('mousemove', handleFilterResize);
    document.removeEventListener('mouseup', handleFilterResizeEnd);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleFilterResizeStart = useCallback((e: React.MouseEvent) => {
    isDraggingFilter.current = true;
    startY.current = e.clientY;
    startHeight.current = filterPanelHeight;
    document.body.style.cursor = 'ns-resize';
    document.body.style.userSelect = 'none';
    document.addEventListener('mousemove', handleFilterResize);
    document.addEventListener('mouseup', handleFilterResizeEnd);
    e.preventDefault();
  }, [filterPanelHeight, handleFilterResize, handleFilterResizeEnd]);

  const handleTableResize = useCallback((e: MouseEvent) => {
    if (!isDraggingTable.current) return;
    const deltaY = startY.current - e.clientY; // Inverted for bottom panel
    const newHeight = Math.max(50, Math.min(500, startHeight.current + deltaY));
    setDataTableHeight(newHeight);
  }, []);

  const handleTableResizeEnd = useCallback(() => {
    isDraggingTable.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.removeEventListener('mousemove', handleTableResize);
    document.removeEventListener('mouseup', handleTableResizeEnd);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleTableResizeStart = useCallback((e: React.MouseEvent) => {
    isDraggingTable.current = true;
    startY.current = e.clientY;
    startHeight.current = dataTableHeight;
    document.body.style.cursor = 'ns-resize';
    document.body.style.userSelect = 'none';
    document.addEventListener('mousemove', handleTableResize);
    document.addEventListener('mouseup', handleTableResizeEnd);
    e.preventDefault();
  }, [dataTableHeight, handleTableResize, handleTableResizeEnd]);

  return (
    <div className="main-page">
      {/* Top Filter Panel */}
      <div
        className={`filter-panel-container ${isFilterPanelCollapsed ? 'collapsed' : ''}`}
        style={{ height: isFilterPanelCollapsed ? '50px' : `${filterPanelHeight}px` }}
      >
        <div className="panel-header">
          <h3>Filters & Controls</h3>
          <button
            className="collapse-button"
            onClick={() => setIsFilterPanelCollapsed(!isFilterPanelCollapsed)}
          >
            {isFilterPanelCollapsed ? '▼' : '▲'}
          </button>
        </div>
        {!isFilterPanelCollapsed && (
          <div className="panel-content">
            <FilterPanel
              onFiltersChange={handleFiltersChange}
              filterOptions={filterOptions}
              loading={optionsLoading}
              currentFilters={currentFilters}
            />
          </div>
        )}
        {!isFilterPanelCollapsed && (
          <div
            className="resize-handle resize-handle-bottom"
            onMouseDown={handleFilterResizeStart}
          />
        )}
      </div>

      {/* Main 3D Visualization */}
      <div className="visualization-container">
        <Visualization3D
          data={visualizationData}
          loading={dataLoading}
          error={dataError}
          selectedPoints={selectedPoints}
          onPointSelect={handlePointSelect}
        />
      </div>

      {/* Bottom Data Table */}
      <div
        className={`data-table-container ${isDataTableCollapsed ? 'collapsed' : ''}`}
        style={{ height: isDataTableCollapsed ? '50px' : `${dataTableHeight}px` }}
      >
        {!isDataTableCollapsed && (
          <div
            className="resize-handle resize-handle-top"
            onMouseDown={handleTableResizeStart}
          />
        )}
        <div className="panel-header">
          <h3>Data Points</h3>
          <button
            className="collapse-button"
            onClick={() => setIsDataTableCollapsed(!isDataTableCollapsed)}
          >
            {isDataTableCollapsed ? '▲' : '▼'}
          </button>
        </div>
        {!isDataTableCollapsed && (
          <div className="panel-content">
            <DataTable
              data={visualizationData?.points || []}
              loading={dataLoading}
              selectedRows={selectedPoints}
              onRowSelect={handlePointSelect}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MainPage;
