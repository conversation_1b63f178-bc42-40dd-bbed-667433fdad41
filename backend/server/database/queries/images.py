"""Query functions for Image table operations."""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..tables import Image, Point
from ..utils import current_timestamp_ms


def create_image(
    db: Session,
    user_id: int,
    captured_at: int,
    height: int,
    width: int,
    robot_id: str,
    latitude: Optional[float] = None,
    longitude: Optional[float] = None,
    file_path: Optional[str] = None,
    file_size: Optional[int] = None,
) -> Image:
    """Create a new image record."""
    timestamp = current_timestamp_ms()

    image = Image(
        user_id=user_id,
        captured_at=captured_at,
        latitude=latitude,
        longitude=longitude,
        height=height,
        width=width,
        robot_id=robot_id,
        file_path=file_path,
        file_size=file_size,
        created_at=timestamp,
        updated_at=timestamp,
    )

    db.add(image)
    db.commit()
    db.refresh(image)
    return image


def get_image_by_id(db: Session, image_id: int) -> Optional[Image]:
    """Get an image by its ID."""
    return db.query(Image).filter(Image.id == image_id).first()


def get_images_by_user(
    db: Session,
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    filters: Optional[Dict[str, Any]] = None,
) -> List[Image]:
    """Get images for a user with optional filtering."""
    query = db.query(Image).filter(Image.user_id == user_id)

    if filters:
        # Time range filtering
        if "captured_after" in filters:
            query = query.filter(Image.captured_at >= filters["captured_after"])
        if "captured_before" in filters:
            query = query.filter(Image.captured_at <= filters["captured_before"])

        # Geographic filtering
        if "lat_min" in filters:
            query = query.filter(Image.latitude >= filters["lat_min"])
        if "lat_max" in filters:
            query = query.filter(Image.latitude <= filters["lat_max"])
        if "lng_min" in filters:
            query = query.filter(Image.longitude >= filters["lng_min"])
        if "lng_max" in filters:
            query = query.filter(Image.longitude <= filters["lng_max"])

        # Robot filtering
        if "robot_id" in filters:
            if isinstance(filters["robot_id"], list):
                query = query.filter(Image.robot_id.in_(filters["robot_id"]))
            else:
                query = query.filter(Image.robot_id == filters["robot_id"])

        # Image dimension filtering
        if "height_min" in filters:
            query = query.filter(Image.height >= filters["height_min"])
        if "height_max" in filters:
            query = query.filter(Image.height <= filters["height_max"])
        if "width_min" in filters:
            query = query.filter(Image.width >= filters["width_min"])
        if "width_max" in filters:
            query = query.filter(Image.width <= filters["width_max"])

    return query.offset(skip).limit(limit).all()


def get_image_count_by_user(
    db: Session,
    user_id: int,
    filters: Optional[Dict[str, Any]] = None,
) -> int:
    """Get count of images for a user with optional filtering."""
    query = db.query(func.count(Image.id)).filter(Image.user_id == user_id)

    if filters:
        # Apply same filters as get_images_by_user
        if "captured_after" in filters:
            query = query.filter(Image.captured_at >= filters["captured_after"])
        if "captured_before" in filters:
            query = query.filter(Image.captured_at <= filters["captured_before"])
        if "lat_min" in filters:
            query = query.filter(Image.latitude >= filters["lat_min"])
        if "lat_max" in filters:
            query = query.filter(Image.latitude <= filters["lat_max"])
        if "lng_min" in filters:
            query = query.filter(Image.longitude >= filters["lng_min"])
        if "lng_max" in filters:
            query = query.filter(Image.longitude <= filters["lng_max"])
        if "robot_id" in filters:
            if isinstance(filters["robot_id"], list):
                query = query.filter(Image.robot_id.in_(filters["robot_id"]))
            else:
                query = query.filter(Image.robot_id == filters["robot_id"])
        if "height_min" in filters:
            query = query.filter(Image.height >= filters["height_min"])
        if "height_max" in filters:
            query = query.filter(Image.height <= filters["height_max"])
        if "width_min" in filters:
            query = query.filter(Image.width >= filters["width_min"])
        if "width_max" in filters:
            query = query.filter(Image.width <= filters["width_max"])

    return query.scalar()


def get_image_with_points_count(db: Session, image_id: int) -> Optional[Dict[str, Any]]:
    """Get an image with its point count."""
    image = get_image_by_id(db, image_id)
    if not image:
        return None

    point_count = (
        db.query(func.count(Point.id)).filter(Point.image_id == image_id).scalar()
    )

    return {
        "id": image.id,
        "user_id": image.user_id,
        "captured_at": image.captured_at,
        "latitude": image.latitude,
        "longitude": image.longitude,
        "height": image.height,
        "width": image.width,
        "robot_id": image.robot_id,
        "file_path": image.file_path,
        "file_size": image.file_size,
        "created_at": image.created_at,
        "updated_at": image.updated_at,
        "total_points": point_count,
    }


def delete_image(db: Session, image_id: int) -> bool:
    """Delete an image and all its related data."""
    image = get_image_by_id(db, image_id)
    if not image:
        return False

    db.delete(image)
    db.commit()
    return True


def get_unique_robot_ids(db: Session, user_id: int) -> List[str]:
    """Get list of unique robot IDs for a user."""
    result = db.query(Image.robot_id).filter(Image.user_id == user_id).distinct().all()
    return [row[0] for row in result]
