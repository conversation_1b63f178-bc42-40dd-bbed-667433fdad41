"""Query functions for Point table operations."""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..tables import Point, Image
from ..utils import current_timestamp_ms


def create_point(
    db: Session,
    image_id: int,
    x: float,
    y: float,
    radius: float,
) -> Point:
    """Create a new point record."""
    timestamp = current_timestamp_ms()

    point = Point(
        image_id=image_id,
        x=x,
        y=y,
        radius=radius,
        created_at=timestamp,
        updated_at=timestamp,
    )

    db.add(point)
    db.commit()
    db.refresh(point)
    return point


def get_point_by_id(db: Session, point_id: int) -> Optional[Point]:
    """Get a point by its ID."""
    return db.query(Point).filter(Point.id == point_id).first()


def get_points_by_image(
    db: Session,
    image_id: int,
    skip: int = 0,
    limit: int = 1000,
    filters: Optional[Dict[str, Any]] = None,
) -> List[Point]:
    """Get points for an image with optional filtering."""
    query = db.query(Point).filter(Point.image_id == image_id)

    if filters:
        # Coordinate filtering
        if "x_min" in filters:
            query = query.filter(Point.x >= filters["x_min"])
        if "x_max" in filters:
            query = query.filter(Point.x <= filters["x_max"])
        if "y_min" in filters:
            query = query.filter(Point.y >= filters["y_min"])
        if "y_max" in filters:
            query = query.filter(Point.y <= filters["y_max"])

        # Radius filtering
        if "radius_min" in filters:
            query = query.filter(Point.radius >= filters["radius_min"])
        if "radius_max" in filters:
            query = query.filter(Point.radius <= filters["radius_max"])

    return query.offset(skip).limit(limit).all()



def get_point_count_by_image(db: Session, image_id: int) -> int:
    """Get count of points for an image."""
    return db.query(func.count(Point.id)).filter(Point.image_id == image_id).scalar()


def delete_point(db: Session, point_id: int) -> bool:
    """Delete a point and all its related data."""
    point = get_point_by_id(db, point_id)
    if not point:
        return False

    db.delete(point)
    db.commit()
    return True


def create_points_batch(
    db: Session,
    image_id: int,
    points_data: List[Dict[str, float]],
) -> List[Point]:
    """Create multiple points for an image in a batch."""
    timestamp = current_timestamp_ms()

    points = []
    for point_data in points_data:
        point = Point(
            image_id=image_id,
            x=point_data["x"],
            y=point_data["y"],
            radius=point_data["radius"],
            created_at=timestamp,
            updated_at=timestamp,
        )
        points.append(point)

    db.add_all(points)
    db.commit()

    for point in points:
        db.refresh(point)

    return points
