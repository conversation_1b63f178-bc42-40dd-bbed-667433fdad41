"""SQLAlchemy table definitions for Loom application."""

import time
from sqlalchemy import (
    Column,
    Integer,
    BigInteger,
    String,
    Float,
    ForeignKey,
    Boolean,
    JSON,
)
from sqlalchemy.orm import relationship
from .connection import Base


class BaseModel(Base):
    """Base model with common timestamp fields."""

    __abstract__ = True

    created_at = Column(
        BigInteger, nullable=False, default=lambda: int(time.time() * 1000)
    )
    updated_at = Column(
        BigInteger,
        nullable=False,
        default=lambda: int(time.time() * 1000),
        onupdate=lambda: int(time.time() * 1000),
    )


class User(BaseModel):
    """User table for authentication and user management."""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    images = relationship("Image", back_populates="owner", cascade="all, delete-orphan")


class Image(BaseModel):
    """Image table for storing image metadata and capture information."""

    __tablename__ = "images"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Image capture metadata
    captured_at = Column(BigInteger, nullable=False)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    height = Column(Integer, nullable=False)  # Image height in pixels
    width = Column(Integer, nullable=False)  # Image width in pixels
    robot_id = Column(String(255), nullable=False, index=True)

    # Optional file storage
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)

    owner = relationship("User", back_populates="images")
    points = relationship("Point", back_populates="image", cascade="all, delete-orphan")


class Point(BaseModel):
    """Point table for storing annotated points on images."""

    __tablename__ = "points"

    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey("images.id"), nullable=False)

    # Point coordinates and properties
    x = Column(Float, nullable=False)  # X coordinate on image
    y = Column(Float, nullable=False)  # Y coordinate on image
    radius = Column(Float, nullable=False)  # Radius of the point

    image = relationship("Image", back_populates="points")
    embeddings = relationship(
        "Embedding", back_populates="point", cascade="all, delete-orphan"
    )


class Embedding(BaseModel):
    """Embedding table for storing vector embeddings from ML models."""

    __tablename__ = "embeddings"

    id = Column(Integer, primary_key=True, index=True)
    point_id = Column(Integer, ForeignKey("points.id"), nullable=False)

    # Model information
    model_id = Column(String(255), nullable=False, index=True)

    # Embedding data
    vector = Column(JSON, nullable=False)
    dimensions = Column(Integer, nullable=False)

    point = relationship("Point", back_populates="embeddings")
    reduced_embeddings = relationship(
        "ReducedEmbedding", back_populates="embedding", cascade="all, delete-orphan"
    )


class ReducedEmbedding(BaseModel):
    """ReducedEmbedding table for storing dimensionality-reduced embeddings for visualization."""

    __tablename__ = "reduced_embeddings"

    id = Column(Integer, primary_key=True, index=True)
    embedding_id = Column(Integer, ForeignKey("embeddings.id"), nullable=False)

    # Reduction method and parameters
    method = Column(
        String(255), nullable=False, index=True
    )  # e.g., "umap_3d", "tsne_2d"
    method_params = Column(JSON, nullable=True)

    # Reduced coordinates
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    z = Column(Float, nullable=True)  # Optional for 2D reductions

    embedding = relationship("Embedding", back_populates="reduced_embeddings")
